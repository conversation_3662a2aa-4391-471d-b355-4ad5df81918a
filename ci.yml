Global:
  version: 2.0
Default:
  profile: [dev]
Profiles:
  - profile:
    name: dev
    mode: AGENT
    environment:
      image: DECK_CENTOS7U5_K3
      tools:
        - nodejs: 18.3.0
    build:
      command: /bin/bash ./scripts/build.dev.sh dev
    artifacts:
      release: false
  - profile:
    name: test
    mode: AGENT
    environment:
      image: DECK_CENTOS7U5_K3
      tools:
        - nodejs: 18.3.0
    build:
      command: /bin/bash ./scripts/build.test.sh test
    artifacts:
      release: false
  - profile:
    name: master
    mode: AGENT
    environment:
      image: DECK_CENTOS7U5_K3
      tools:
        - nodejs: 18.3.0
    build:
      command: /bin/bash ./scripts/build.sh master
    artifacts:
      release: false
  - profile:
    name: httpsmaster
    mode: AGENT
    environment:
      image: DECK_CENTOS7U5_K3
      tools:
        - nodejs: 18.3.0
    build:
      command: /bin/bash ./scripts/build.prod.sh httpsmaster
    artifacts:
      release: false
  - profile:
    name: idemaster
    mode: AGENT
    environment:
      image: DECK_CENTOS7U5_K3
      tools:
        - nodejs: 18.3.0
    build:
      command: /bin/bash ./scripts/build-ide.sh
    artifacts:
      release: false
  - profile:
    name: ragdev
    mode: AGENT
    environment:
      image: DECK_CENTOS7U5_K3
      tools:
        - nodejs: 18.3.0
    build:
      command: /bin/bash ./scripts/build-rag.dev.sh rag_dev
    artifacts:
      release: true
  - profile:
    name: rag_master
    mode: AGENT
    environment:
      image: DECK_CENTOS7U5_K3
      tools:
        - nodejs: 18.3.0
    build:
      command: /bin/bash ./scripts/build-rag.sh rag_master
    artifacts:
      release: true